<?php
// SolusVM Diagnostic Script
// Run this to identify the correct IDs for your mappings

$apiKey = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
$baseUrl = "https://virt.zetservers.com";

echo "=== SolusVM API Diagnostic Tool ===\n\n";

function makeApiCall($endpoint, $apiKey, $baseUrl) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $baseUrl . $endpoint);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        "Authorization: Bearer " . $apiKey,
        "Accept: application/json",
    ]);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode == 200) {
        return json_decode($response, true);
    } else {
        return ["error" => "HTTP $httpCode", "response" => $response];
    }
}

// 1. Get Projects
echo "1. PROJECTS:\n";
echo "------------\n";
$projects = makeApiCall("/api/v1/projects", $apiKey, $baseUrl);
if (isset($projects['data'])) {
    foreach ($projects['data'] as $project) {
        echo "ID: {$project['id']} | Name: {$project['name']}\n";
    }
} else {
    echo "Error fetching projects: " . json_encode($projects) . "\n";
}
echo "\n";

// 2. Get Plans
echo "2. PLANS:\n";
echo "---------\n";
$plans = makeApiCall("/api/v1/plans", $apiKey, $baseUrl);
if (isset($plans['data'])) {
    foreach ($plans['data'] as $plan) {
        echo "ID: {$plan['id']} | Name: {$plan['name']} | CPU: {$plan['limits']['cpu_limit']} | RAM: " . 
             round($plan['limits']['memory_limit'] / 1024 / 1024 / 1024, 2) . "GB | Disk: " . 
             round($plan['limits']['disk_limit'] / 1024 / 1024 / 1024, 2) . "GB\n";
    }
} else {
    echo "Error fetching plans: " . json_encode($plans) . "\n";
}
echo "\n";

// 3. Get Locations
echo "3. LOCATIONS:\n";
echo "-------------\n";
$locations = makeApiCall("/api/v1/locations", $apiKey, $baseUrl);
if (isset($locations['data'])) {
    foreach ($locations['data'] as $location) {
        echo "ID: {$location['id']} | Name: {$location['name']} | Description: {$location['description']}\n";
    }
} else {
    echo "Error fetching locations: " . json_encode($locations) . "\n";
}
echo "\n";

// 4. Get OS Images
echo "4. OS IMAGES:\n";
echo "-------------\n";
$os_images = makeApiCall("/api/v1/os_images", $apiKey, $baseUrl);
if (isset($os_images['data'])) {
    foreach ($os_images['data'] as $os) {
        echo "OS: {$os['name']} (ID: {$os['id']})\n";
        if (isset($os['versions'])) {
            foreach ($os['versions'] as $version) {
                echo "  - Version ID: {$version['id']} | Version: {$version['version']} | Is Default: " . 
                     ($version['is_default'] ? 'YES' : 'NO') . "\n";
            }
        }
    }
} else {
    echo "Error fetching OS images: " . json_encode($os_images) . "\n";
}
echo "\n";

// 5. Get Compute Resources
echo "5. COMPUTE RESOURCES:\n";
echo "--------------------\n";
$compute_resources = makeApiCall("/api/v1/compute_resources", $apiKey, $baseUrl);
if (isset($compute_resources['data'])) {
    foreach ($compute_resources['data'] as $resource) {
        echo "ID: {$resource['id']} | Name: {$resource['name']} | Type: {$resource['type']} | Status: {$resource['status']}\n";
    }
} else {
    echo "Error fetching compute resources: " . json_encode($compute_resources) . "\n";
}
echo "\n";

// 6. Test Server Creation Endpoint
echo "6. TESTING SERVER CREATION ENDPOINT:\n";
echo "------------------------------------\n";
if (isset($projects['data'][0])) {
    $test_project_id = $projects['data'][0]['id'];
    $endpoint = "/api/v1/projects/{$test_project_id}/servers";
    echo "Testing endpoint: $baseUrl$endpoint\n";
    echo "This is the endpoint that would be used for server creation.\n";
} else {
    echo "Cannot test - no projects found.\n";
}

echo "\n=== MAPPING RECOMMENDATIONS ===\n";
echo "Based on the above data, update your mapping arrays in the code:\n\n";

// Generate mapping code
if (isset($plans['data']) && count($plans['data']) > 0) {
    echo "// Plan mappings (your_plan_id => solus_plan_id)\n";
    echo '$plan_mapping = [' . "\n";
    foreach ($plans['data'] as $i => $plan) {
        $comment = " // {$plan['name']}";
        echo "    " . ($i + 1) . " => {$plan['id']},$comment\n";
    }
    echo "];\n\n";
}

if (isset($locations['data']) && count($locations['data']) > 0) {
    echo "// Location mappings (your_location_id => solus_location_id)\n";
    echo '$location_mapping = [' . "\n";
    foreach ($locations['data'] as $i => $location) {
        $comment = " // {$location['name']}";
        echo "    " . ($i + 1) . " => {$location['id']},$comment\n";
    }
    echo "];\n\n";
}

if (isset($os_images['data']) && count($os_images['data']) > 0) {
    echo "// OS mappings (your_os_id => solus_os_version_id)\n";
    echo '$os_mapping = [' . "\n";
    $os_count = 1;
    foreach ($os_images['data'] as $os) {
        if (isset($os['versions'])) {
            foreach ($os['versions'] as $version) {
                if ($version['is_default']) {
                    $comment = " // {$os['name']} {$version['version']}";
                    echo "    {$os_count} => {$version['id']},$comment\n";
                    $os_count++;
                    break; // Only use default version
                }
            }
        }
    }
    echo "];\n";
}

echo "\n=== END OF DIAGNOSTIC ===\n";